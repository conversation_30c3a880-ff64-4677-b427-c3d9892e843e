fn main() {
    // Минимальный build.rs для статической библиотеки
    println!("cargo:rerun-if-changed=src/lib.rs"); // пересобирать при изменении файла
    println!("cargo:rerun-if-changed=src/canonical_rust.rs");
    println!("cargo:rerun-if-changed=src/ffi.rs");
    println!("cargo:rerun-if-changed=src/date_and_time_canonical.rs");
    println!("cargo:rerun-if-changed=src/date_and_time_ffi.rs");
    println!("cargo:rerun-if-changed=src/commissioning_canonical.rs");
    println!("cargo:rerun-if-changed=src/commissioning_ffi.rs");
}
