/**
 * Rust implementation of device_id module
 * 
 * Module implements functions, related to device ID.
 * 
 * ID in bork smart home system consists of 10 bytes:
 * 4 random bytes, and 6 bytes of device mac address
 * ID is generated once, and then stored in NVS
 * 
 * Also there is guid - 53 + 1(string end) symbols string in format
 * bork_device_a830_cac4b175-fffe-4b19-8359-456d8279b87b
 * text after a830 is randomly generated, and not saved
 */

#pragma once

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

#define DEVICE_ID_SIZE 10
#define GUID_SIZE 54

/**
 * @brief Returns pointer to device id.
 * ID consists of 4 bytes of random + 6 bytes of mac address 
 * Generates it if not generated before, then saves
 * Loads ID if it was generated before
 * 
 * BLE must be init first !!! as this function requests BLE mac
 * @return Pointer to device ID buffer (DEVICE_ID_SIZE bytes)
 */
const uint8_t *get_device_id(void);

/**
 * @brief Write device id to buffer
 * @param dest_buffer pointer to where write the ID (should be DEVICE_ID_SIZE bytes)
 */
void write_device_id_to_buffer(uint8_t *dest_buffer);

/**
 * @brief writes device id to buffer - in string format
 * @param buffer buffer to write id to. should be 21 bytes long
 * @return amount of written symbols, excluding null-terminator
 */
int write_device_id_to_buffer_as_string(char *buffer);

/**
 * @brief Generates GUID in format of 53 + 1(string end) symbol string: 
 * bork_device_q781_cac4b175-fffe-4b19-8359-456d8279b87b
 * for mqtt client id
 * 
 * Writes guid to provided buffer
 * 
 * @param guid_prefix device-specific prefix of the guid
 * @param buff buffer of 54 (GUID_SIZE) bytes size to store the id 
 */ 
void generate_GUID(const char *guid_prefix, char *buff);

/**
 * @brief same as generate_GUID, but stores it internally and returns pointer
 * @param guid_prefix device-specific prefix of the guid
 * @return Pointer to GUID string
 * @note The returned pointer is valid until the next call to this function
 */
const char *get_guid(const char *guid_prefix);

#ifdef __cplusplus
}
#endif
