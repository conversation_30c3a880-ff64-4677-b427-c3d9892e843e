/**
 * Rust implementation of commissioning module
 * 
 * This module receives, saves and loads commissioning credentials:
 * - WiFi SSID & password
 * - MQTT broker login and password  
 * - Balancer link
 * - Device ID management
 */

#pragma once

#include "esp_err.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

#define DEVICE_ID_SIZE 10

/**
 * @brief Initialize commissioning module
 * Loads all stored credentials from NVS
 * 
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t commissioning_init(void);

// WiFi credentials functions

/**
 * @brief Set WiFi SSID
 * @param ssid WiFi network name (null-terminated string)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t commissioning_set_wifi_ssid(const char *ssid);

/**
 * @brief Set WiFi password
 * @param password WiFi password (null-terminated string)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t commissioning_set_wifi_passw(const char *password);

/**
 * @brief Get WiFi SSID
 * @return Pointer to WiFi SSID string, or NULL if not set
 * @note The returned pointer is valid until the next call to this function
 */
const char *commissioning_get_wifi_ssid(void);

/**
 * @brief Get WiFi password
 * @return Pointer to WiFi password string, or NULL if not set
 * @note The returned pointer is valid until the next call to this function
 */
const char *commissioning_get_wifi_passw(void);

// MQTT credentials functions

/**
 * @brief Set MQTT user ID
 * @param user_id MQTT user ID (null-terminated string)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t commissioning_set_mqtt_user_id(const char *user_id);

/**
 * @brief Set MQTT password
 * @param password MQTT password (null-terminated string)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t commissioning_set_mqtt_passw(const char *password);

/**
 * @brief Get MQTT user ID
 * @return Pointer to MQTT user ID string, or NULL if not set
 * @note The returned pointer is valid until the next call to this function
 */
const char *commissioning_get_mqtt_user_id(void);

/**
 * @brief Get MQTT password
 * @return Pointer to MQTT password string, or NULL if not set
 * @note The returned pointer is valid until the next call to this function
 */
const char *commissioning_get_mqtt_passw(void);

// Balancer functions

/**
 * @brief Set balancer link
 * @param link Balancer URL (null-terminated string)
 * @return ESP_OK on success, error code otherwise
 */
esp_err_t commissioning_set_balancer_link(const char *link);

/**
 * @brief Get balancer link
 * @return Pointer to balancer link string, or NULL if not set
 * @note The returned pointer is valid until the next call to this function
 */
const char *commissioning_get_balancerlink(void);

// Device ID functions

/**
 * @brief Get device ID as string
 * Device ID consists of 4 random bytes + 6 bytes of MAC address
 * @return Pointer to device ID string (20 hex characters + null terminator)
 * @note The returned pointer is valid until the next call to this function
 */
const char *commissioning_get_dev_id_as_string(void);

/**
 * @brief Write device ID to buffer
 * @param buffer Buffer to write device ID to (must be at least DEVICE_ID_SIZE bytes)
 * @note Device ID consists of 4 random bytes + 6 bytes of MAC address
 */
void commissioning_write_device_id_to_buffer(uint8_t *buffer);

#ifdef __cplusplus
}
#endif
