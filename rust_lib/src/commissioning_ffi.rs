#![allow(non_camel_case_types)]

use std::ffi::{CStr, CString};
use std::os::raw::c_char;
use std::ptr;

use esp_idf_sys::{esp_err_t, ESP_OK, ESP_ERR_INVALID_ARG, ESP_ERR_NOT_FOUND};

use crate::commissioning_canonical::{
    init, set_wifi_ssid, set_wifi_password, get_wifi_ssid, get_wifi_password,
    set_mqtt_user_id, set_mqtt_password, get_mqtt_user_id, get_mqtt_password,
    set_balancer_link, get_balancer_link, get_device_id_as_string,
    write_device_id_to_buffer, DEVICE_ID_SIZE,
};

/// Статические буферы для возврата строк в C код
static mut WIFI_SSID_BUFFER: Option<CString> = None;
static mut WIFI_PASSWORD_BUFFER: Option<CString> = None;
static mut MQTT_USER_ID_BUFFER: Option<CString> = None;
static mut MQTT_PASSWORD_BUFFER: Option<CString> = None;
static mut BALANCER_LINK_BUFFER: Option<CString> = None;
static mut DEVICE_ID_STRING_BUFFER: Option<CString> = None;

/// Инициализация commissioning модуля
#[no_mangle]
pub extern "C" fn commissioning_init() -> esp_err_t {
    match init() {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}

/// Установка WiFi SSID
#[no_mangle]
pub extern "C" fn commissioning_set_wifi_ssid(ssid: *const c_char) -> esp_err_t {
    if ssid.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(ssid) };
    let ssid_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match set_wifi_ssid(ssid_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}

/// Установка WiFi пароля
#[no_mangle]
pub extern "C" fn commissioning_set_wifi_passw(password: *const c_char) -> esp_err_t {
    if password.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(password) };
    let password_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match set_wifi_password(password_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}

/// Получение WiFi SSID
#[no_mangle]
pub extern "C" fn commissioning_get_wifi_ssid() -> *const c_char {
    match get_wifi_ssid() {
        Some(ssid) => {
            match CString::new(ssid) {
                Ok(cstring) => {
                    unsafe {
                        WIFI_SSID_BUFFER = Some(cstring);
                        WIFI_SSID_BUFFER.as_ref().unwrap().as_ptr()
                    }
                }
                Err(_) => ptr::null(),
            }
        }
        None => ptr::null(),
    }
}

/// Получение WiFi пароля
#[no_mangle]
pub extern "C" fn commissioning_get_wifi_passw() -> *const c_char {
    match get_wifi_password() {
        Some(password) => {
            match CString::new(password) {
                Ok(cstring) => {
                    unsafe {
                        WIFI_PASSWORD_BUFFER = Some(cstring);
                        WIFI_PASSWORD_BUFFER.as_ref().unwrap().as_ptr()
                    }
                }
                Err(_) => ptr::null(),
            }
        }
        None => ptr::null(),
    }
}

/// Установка MQTT user ID
#[no_mangle]
pub extern "C" fn commissioning_set_mqtt_user_id(user_id: *const c_char) -> esp_err_t {
    if user_id.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(user_id) };
    let user_id_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match set_mqtt_user_id(user_id_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}

/// Установка MQTT пароля
#[no_mangle]
pub extern "C" fn commissioning_set_mqtt_passw(password: *const c_char) -> esp_err_t {
    if password.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(password) };
    let password_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match set_mqtt_password(password_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}

/// Получение MQTT user ID
#[no_mangle]
pub extern "C" fn commissioning_get_mqtt_user_id() -> *const c_char {
    match get_mqtt_user_id() {
        Some(user_id) => {
            match CString::new(user_id) {
                Ok(cstring) => {
                    unsafe {
                        MQTT_USER_ID_BUFFER = Some(cstring);
                        MQTT_USER_ID_BUFFER.as_ref().unwrap().as_ptr()
                    }
                }
                Err(_) => ptr::null(),
            }
        }
        None => ptr::null(),
    }
}

/// Получение MQTT пароля
#[no_mangle]
pub extern "C" fn commissioning_get_mqtt_passw() -> *const c_char {
    match get_mqtt_password() {
        Some(password) => {
            match CString::new(password) {
                Ok(cstring) => {
                    unsafe {
                        MQTT_PASSWORD_BUFFER = Some(cstring);
                        MQTT_PASSWORD_BUFFER.as_ref().unwrap().as_ptr()
                    }
                }
                Err(_) => ptr::null(),
            }
        }
        None => ptr::null(),
    }
}

/// Установка balancer link
#[no_mangle]
pub extern "C" fn commissioning_set_balancer_link(link: *const c_char) -> esp_err_t {
    if link.is_null() {
        return ESP_ERR_INVALID_ARG;
    }

    let cstr = unsafe { CStr::from_ptr(link) };
    let link_str = match cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ESP_ERR_INVALID_ARG,
    };

    match set_balancer_link(link_str) {
        Ok(()) => ESP_OK,
        Err(_) => ESP_ERR_NOT_FOUND,
    }
}

/// Получение balancer link
#[no_mangle]
pub extern "C" fn commissioning_get_balancerlink() -> *const c_char {
    match get_balancer_link() {
        Some(link) => {
            match CString::new(link) {
                Ok(cstring) => {
                    unsafe {
                        BALANCER_LINK_BUFFER = Some(cstring);
                        BALANCER_LINK_BUFFER.as_ref().unwrap().as_ptr()
                    }
                }
                Err(_) => ptr::null(),
            }
        }
        None => ptr::null(),
    }
}

/// Получение device ID как строки
#[no_mangle]
pub extern "C" fn commissioning_get_dev_id_as_string() -> *const c_char {
    let device_id_string = get_device_id_as_string();
    match CString::new(device_id_string) {
        Ok(cstring) => {
            unsafe {
                DEVICE_ID_STRING_BUFFER = Some(cstring);
                DEVICE_ID_STRING_BUFFER.as_ref().unwrap().as_ptr()
            }
        }
        Err(_) => ptr::null(),
    }
}

/// Запись device ID в буфер
#[no_mangle]
pub extern "C" fn commissioning_write_device_id_to_buffer(buffer: *mut u8) {
    if buffer.is_null() {
        return;
    }

    unsafe {
        let buffer_slice = std::slice::from_raw_parts_mut(buffer, DEVICE_ID_SIZE);
        write_device_id_to_buffer(buffer_slice);
    }
}
