use std::sync::RwLock;
use time::{Duration, OffsetDateTime};
use time::format_description::well_known::Rfc2822;
use time::format_description::FormatItem;
use time::macros::format_description;

use esp_idf_sys::esp_timer_get_time;

// Глобальная переменная (локальная для крейта)
struct StoredTime {
    dt: OffsetDateTime,
    esp_time_us: i64,
}

static DATE_AND_TIME: RwLock<Option<StoredTime>> = RwLock::new(None);



pub(crate) fn parse_and_store_http_date(date_str: &str) -> Result<(), time::error::Parse> {
    let dt = OffsetDateTime::parse(date_str, &Rfc2822)?;
    let esp_time_us = unsafe { esp_timer_get_time() };

    *DATE_AND_TIME.write().unwrap() = Some(StoredTime { dt, esp_time_us });
    Ok(())
}



pub fn get_date() -> Option<OffsetDateTime> {
    let now_esp_time_us = unsafe { esp_timer_get_time() };

    DATE_AND_TIME.read().unwrap().as_ref().map(|stored| {
        let elapsed_us = now_esp_time_us - stored.esp_time_us;
        stored.dt + Duration::microseconds(elapsed_us)
    })
}



pub fn current_datetime_string() -> String {
    let now = get_date().unwrap_or_else(|| OffsetDateTime::now_utc());

    // Формат yyyy-mm-ddThh:mm:ss
    let format: &[FormatItem<'_>] = format_description!(
        "[year]-[month]-[day]T[hour]:[minute]:[second]"
    );

    now.format(format).unwrap()
}